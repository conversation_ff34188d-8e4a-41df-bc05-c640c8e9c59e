from django.urls import path
from . import views

urlpatterns = [
    path('', views.home_view, name='root'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.profile_view, name='profile'),
    path('home/', views.home_view, name='home'),
    path('tables-dashboard/', views.tables_dashboard_view, name='tables_dashboard'),
    path('data-statistics/', views.data_statistics_view, name='data_statistics'),

    path('medical-evacuation/', views.medical_evacuation_view, name='medical_evacuation'),
    path('medical-evacuation/debug/', views.debug_medical_evacuation_view, name='debug_medical_evacuation'),
    path('traffic-accidents/', views.traffic_accidents_view, name='traffic_accidents'),

    path('fires/', views.fires_view, name='fires'),
    path('fires/general-fire-table/', views.general_fire_table_view, name='general_fire_table'),
    path('fires/residential-fires/', views.residential_fires_view, name='residential_fires'),
    path('fires/institutional-fires/', views.institutional_fires_view, name='institutional_fires'),
    path('fires/public-area-fires/', views.public_area_fires_view, name='public_area_fires'),
    path('fires/forest-agricultural-fires/', views.forest_agricultural_fires_view, name='forest_agricultural_fires'),
    path('misc-operations/', views.misc_operations_view, name='misc_operations'),
    path('misc-operations/stats/', views.misc_operations_stats_view, name='misc_operations_stats'),
    path('misc-operations/security-device-stats/', views.security_device_stats_view, name='security_device_stats'),
    path('misc-operations/exceptional-operations-stats/', views.exceptional_operations_stats_view, name='exceptional_operations_stats'),
    path('misc-operations/interventions-without-work-stats/', views.interventions_without_work_stats_view, name='interventions_without_work_stats'),
    path('settings/', views.settings_view, name='settings'),
    path('delete-unit/<int:unit_id>/', views.delete_unit, name='delete_unit'),
    path('coordination-center/', views.coordination_center_view, name='coordination_center'),

    # Coordination Center sub-pages
    path('coordination-center/data-entry-forms/', views.data_entry_forms_view, name='data_entry_forms'),
    path('coordination-center/telegram-forms/', views.telegram_forms_view, name='telegram_forms'),
    path('coordination-center/daily-telegrams/', views.daily_telegrams_view, name='daily_telegrams'),
    path('coordination-center/urgent-telegrams/', views.urgent_telegrams_view, name='urgent_telegrams'),

    path('coordination-center/forest-crop-fires/', views.forest_crop_fires_view, name='forest_crop_fires'),
    path('coordination-center/water-leaks/', views.water_leaks_view, name='water_leaks'),
    # إعادة توجيه الصفحة القديمة للنظام الموحد الجديد
    path('coordination-center/daily-unit-count/', views.redirect_to_unified_system, name='daily_unit_count'),
    path('coordination-center/daily-unit-reports/', views.daily_unit_reports_view, name='daily_unit_reports'),
    path('coordination-center/advanced-morning-check/', views.advanced_morning_check_view, name='advanced_morning_check'),
    path('coordination-center/unified-morning-check/', views.unified_morning_check_view, name='unified_morning_check'),
    path('coordination-center/add-personnel/', views.add_personnel_view, name='add_personnel_page'),
    path('coordination-center/transfer-personnel/', views.transfer_personnel_page, name='transfer_personnel_page'),
    path('coordination-center/shift-schedule/', views.shift_schedule_management, name='shift_schedule_management'),

    # Unified Morning Check API endpoints
    path('api/unified/add-personnel/', views.add_personnel_unified, name='add_personnel_unified'),
    path('api/unified/update-personnel-status/', views.update_personnel_status_unified, name='update_personnel_status_unified'),
    path('api/unified/delete-personnel/', views.delete_personnel_unified, name='delete_personnel_unified'),
    path('api/unified/transfer-personnel/', views.transfer_personnel_unified, name='transfer_personnel_unified'),
    path('api/unified/change-work-system/', views.change_work_system_unified, name='change_work_system_unified'),
    path('api/unified/current-shift/', views.get_current_working_shift_info, name='get_current_working_shift_info'),
    path('api/unified/create-shift-schedule/', views.create_shift_schedule, name='create_shift_schedule'),
    path('api/unified/update-wilaya-shifts/', views.update_wilaya_shifts, name='update_wilaya_shifts'),
    path('api/unified/get-work-systems-info/', views.get_work_systems_info, name='get_work_systems_info'),
    path('api/unified/create-yearly-schedule/', views.create_yearly_schedule, name='create_yearly_schedule'),
    path('api/unified/simple-shift-update/', views.simple_shift_update, name='simple_shift_update'),
    path('api/unified/update-equipment-status/', views.update_equipment_status_unified, name='update_equipment_status_unified'),
    path('api/unified/get-work-status/', views.get_personnel_work_status, name='get_personnel_work_status'),
    path('api/test-connection/', views.test_api_connection, name='test_api_connection'),
    path('coordination-center/add-personnel/', views.add_personnel_view, name='add_personnel'),
    path('coordination-center/add-equipment/', views.add_equipment_view, name='add_equipment'),
    path('coordination-center/manage-roles/', views.manage_roles_view, name='manage_roles'),

    # Major Disasters and Field Operations
    path('major-disasters/', views.major_disasters_view, name='major_disasters'),
    path('field-agent/', views.field_agent_view, name='field_agent'),
    path('unit-leader/', views.unit_leader_view, name='unit_leader'),

    # Forest and Crop Fires sub-pages
    path('coordination-center/forest-fires-form/', views.forest_fires_form_view, name='forest_fires_form'),
    path('coordination-center/crop-fires-form/', views.crop_fires_form_view, name='crop_fires_form'),
    path('coordination-center/forest-crop-tables/', views.forest_crop_tables_view, name='forest_crop_tables'),

    # Forest and Crop Tables sub-pages
    path('coordination-center/forest-fires-table/', views.forest_fires_table_view, name='forest_fires_table'),
    path('coordination-center/crop-fires-table/', views.crop_fires_table_view, name='crop_fires_table'),

    # Export URLs for coordination center
    path('export-coordination-forest-fires/', views.export_coordination_forest_fires_excel, name='export_coordination_forest_fires'),
    path('export-table-to-excel/<str:table_type>/', views.export_table_to_excel, name='export_table_to_excel'),

    # Table pages
    path('tables/general-fire/', views.table_general_fire_view, name='table_general_fire'),
    path('tables/traffic-accidents/', views.table_traffic_accidents_view, name='table_traffic_accidents'),
    path('tables/medical-evacuation/', views.table_medical_evacuation_view, name='table_medical_evacuation'),

    # API endpoints
    path('get-traffic-accident-data/<int:id>/', views.get_traffic_accident_data, name='get_traffic_accident_data'),

    path('tables/public-area-fires/', views.table_public_area_fires_view, name='table_public_area_fires'),
    path('tables/institutional-fires/', views.table_institutional_fires_view, name='table_institutional_fires'),
    path('tables/residential-fires/', views.table_residential_fires_view, name='table_residential_fires'),
    path('tables/exceptional-operations/', views.table_exceptional_operations_view, name='table_exceptional_operations'),
    path('tables/misc-operations/', views.table_misc_operations_view, name='table_misc_operations'),
    path('tables/forest-agricultural-fires/', views.table_forest_agricultural_fires_view, name='table_forest_agricultural_fires'),
    path('tables/interventions-without-work/', views.table_interventions_without_work_view, name='table_interventions_without_work'),

    # Export to Excel
    path('export-table/<str:table_type>/', views.export_table_to_excel, name='export_table_to_excel'),

    # Export statistics to PDF
    path('export-statistics-pdf/', views.export_statistics_pdf, name='export_statistics_pdf'),

    # Vehicle Readiness System
    path('vehicle-readiness/', views.vehicle_readiness_dashboard, name='vehicle_readiness_dashboard'),
    path('vehicle-crew-assignment/', views.vehicle_crew_assignment, name='vehicle_crew_assignment'),

    # Vehicle Readiness API
    path('api/assign-personnel/', views.assign_personnel_to_vehicle, name='assign_personnel_to_vehicle'),
    path('api/remove-personnel/', views.remove_personnel_from_vehicle, name='remove_personnel_from_vehicle'),
    path('api/confirm-readiness/', views.confirm_vehicle_readiness_manually, name='confirm_vehicle_readiness_manually'),
    path('api/mark-vehicle-ready/', views.mark_vehicle_ready, name='mark_vehicle_ready'),
    path('api/update-readiness-from-assignment/', views.update_readiness_from_assignment, name='update_readiness_from_assignment'),

    # Morning Check System URLs
    path('morning-check/', views.morning_check_system_view, name='morning_check_system'),
    path('morning-check/dashboard/', views.morning_check_dashboard_view, name='morning_check_dashboard'),

    # Morning Check System API
    path('api/shift-management/', views.shift_management_view, name='shift_management'),
    path('api/personnel-status-update/', views.personnel_status_update_view, name='personnel_status_update'),
    path('api/eight-hour-personnel/', views.eight_hour_personnel_view, name='eight_hour_personnel'),
    path('api/readiness-alerts/', views.readiness_alerts_view, name='readiness_alerts'),

    # النظام المحدث - وظائف CRUD الجديدة
    path('api/add-personnel/', views.add_personnel_ajax, name='add_personnel_ajax'),
    path('api/delete-personnel/', views.delete_personnel_ajax, name='delete_personnel_ajax'),
    path('api/transfer-personnel/', views.transfer_personnel_ajax, name='transfer_personnel_ajax'),
    path('api/add-equipment/', views.add_equipment_ajax, name='add_equipment_ajax'),
    path('api/delete-equipment/', views.delete_equipment_ajax, name='delete_equipment_ajax'),
    path('api/add-eight-hour-personnel/', views.add_eight_hour_personnel_ajax, name='add_eight_hour_personnel_ajax'),
    path('api/delete-eight-hour-personnel/', views.delete_eight_hour_personnel_ajax, name='delete_eight_hour_personnel_ajax'),

    # APIs التزامن بين الصفحات
    path('api/sync-personnel-status/', views.sync_personnel_status, name='sync_personnel_status'),
    path('api/sync-vehicle-readiness/', views.sync_vehicle_readiness, name='sync_vehicle_readiness'),
    path('api/get-sync-status/', views.get_sync_status, name='get_sync_status'),


]
